<template>
  <UChip
    inset
    :ui="{
      position: {
        'top-right': 'top-2 right-2',
      },
    }"
    color="red"
    :show="hasUnreadNotifications"
  >
    <UButton
      icon="i-fa-bell"
      size="lg"
      color="gray"
      square
      variant="ghost"
      @click="onOpenNotificationSlideover"
    />
  </UChip>

  <NotificationsSlideover
    :notifications="allNotifications"
    @onNotificationDetail="onNotificationDetail"
    @fetchMore="fetchMore"
    :showFetchNext="showFetchNext"
    :isFetching="isFetching"
    @markAllAsRead="markAllAsRead"
  />
</template>

<script setup lang="ts">
import type { RealtimeChannel } from "@supabase/supabase-js";
import md5 from "crypto-js/md5";
const client = useSupabaseClient();
const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const appStore = useAppStore();
const { isNotificationsSlideoverOpen } = storeToRefs(appStore);
const limit = ref(10);
const range = ref({ from: 0, to: limit.value - 1 });
let realtimeChannel: RealtimeChannel;
const notificationsStore = useNotificationsStore();
const router = useRouter();
const route = useRoute();
const runtimeConfig = useRuntimeConfig();
const notificationsTable = runtimeConfig.public.NUXT_NOTIFICATION_TABLE;

const voiceLibraryStore = useVoiceLibraryStore();
const { filter } = storeToRefs(voiceLibraryStore);

// Fetch collaborators and get the refresh method provided by useAsyncData
const { data: allNotifications, refresh: fetchAllNotifications } = await useAsyncData(
  notificationsTable,
  async () => {
    const { data } = await client
      .from(notificationsTable)
      .select("*")
      .eq("user_uuid", user.value?.uuid)
      .gt("status", 1)
      .order("created_at", { ascending: false })
      .range(range.value.from, range.value.to);

    return data?.map((n: any) => {
      // key is md5 hash of the user's email
      const key = md5(user.value?.email);

      const decrypt = aesDecrypt(n?.data, key.toString());
      return {
        ...n,
        ...parseJson(decrypt?.history),
        ...parseJson(decrypt?.order_history),
        ...parseJson(decrypt?.block),
      };
    });
  }
);

const { data: countAll } = await useAsyncData("countNotifications", async () => {
  const { count } = await client
    .from(notificationsTable)
    .select("id", { count: "exact", head: true })
    .eq("user_uuid", user.value?.uuid)
    .gt("status", 1);
  return count;
});

const showFetchNext = computed(() => {
  return allNotifications.value?.length < countAll.value;
});

const isFetching = ref(false);
const fetchMore = async () => {
  isFetching.value = true;
  range.value = {
    from: range.value.from + limit.value,
    to: range.value.to + limit.value,
  };
  const { data } = await client
    .from(notificationsTable)
    .select("*")
    .eq("user_uuid", user.value?.uuid)
    .gt("status", 1)
    .order("created_at", { ascending: false })
    .range(range.value.from, range.value.to);
  const parsedData = data?.map((n: any) => {
    // key is md5 hash of the user's email
    const key = md5(user.value?.email);

    const decrypt = aesDecrypt(n?.data, key.toString());
    return {
      ...n,
      ...parseJson(decrypt?.history),
      ...parseJson(decrypt?.order_history),
      ...parseJson(decrypt?.block),
    };
  });
  allNotifications.value = [...allNotifications.value, ...parsedData];
  isFetching.value = false;
};

const hasUnreadNotifications = computed(() => {
  return allNotifications.value?.filter(notification => ['tts_history', 'voice_training'].includes(notification.event_type))?.some((n) => !n.seen);
});
const historyStore = useHistoryStore();
const translateStore = useTranslateStore();
const { ttsResult } = storeToRefs(translateStore);
onMounted(() => {
  // Real time listener for new workouts
  realtimeChannel = client.channel("changes").on(
    "postgres_changes",
    {
      event: "INSERT",
      table: notificationsTable,
      schema: "public",
      filter: "user_uuid=eq." + user.value?.uuid,
    },
    (payload: any) => {
      const key = md5(user.value?.email);

      const decrypt = aesDecrypt(payload?.new?.data, key.toString());

      const result = {
        ...payload?.new,
        ...parseJson(decrypt?.history),
        ...parseJson(decrypt?.order_history),
        ...parseJson(decrypt?.block),
        redirect_url: decrypt?.redirect_url,
      };
      if (payload?.new && payload?.new?.user_uuid === user.value?.uuid) {
        // replace the new notification with the old one
        allNotifications.value = allNotifications.value?.map((n) => {
          if (n.id === payload.new.id) {
            return result;
          }
          return n;
        });
        // if the notification is new, then unshift it to the top
        if (allNotifications.value?.findIndex((n) => n.id === payload.new.id) === -1) {
          allNotifications.value?.unshift(result);
        }
      }

      if (result?.status === 2 || result?.status === 3) {
        if (ttsResult.value?.uuid) {
          translateStore.fetchTtsResult();
          historyStore.fetchHistoryById(ttsResult.value?.uuid, false);
        } else {
          const translation_uuid = route.query.id;
          if (translation_uuid) {
            historyStore.fetchHistoryById(translation_uuid as string, false);
          } else {
            historyStore.filterHistories(false);
          }
        }

        if(result?.event_type === "voice_training") {
          voiceLibraryStore.fetchVoiceLibraryByType('user_voice' as string, true);
        }
      }

      if (["2", "8"].includes(result?.status) && result?.platform === "CRYPTOMUS") {
        authStore.syncUserTokenInfo();
        // redirect to the payment success page
        router.push("/profile/thank-you?payment=success&id=" + result?.external_order_id);
      }
    }
  );

  realtimeChannel.subscribe();

  // Fetch the data
  fetchAllNotifications();
});

// Don't forget to unsubscribe when user left the page
onUnmounted(() => {
  client.removeChannel(realtimeChannel);
});

const onNotificationDetail = async (row: any) => {
  isNotificationsSlideoverOpen.value = false;

  switch (row.event_type) {
    case "tts_history":
      router.push({ name: "history", query: { id: row.history_uuid } });
      break;
    case "voice_training":
      filter.value.voiceTypes = "user_voice";
      nextTick(() => {
        voiceLibraryStore.filterVoiceLibraries();
      });
      router.push({ name: "voice-library", query: { id: row.history_uuid } });
      break;
    default:
      router.push({ name: "history", query: { id: row.history_uuid } });
      break;
  }

  allNotifications.value = allNotifications.value?.map((n) => {
    if (n.id === row.id) {
      n.seen = true;
    }
    return n;
  });
  await useAPI("/user/read-notification/" + row?.id, {
    method: "PUT",
  });
};

// const onDownloadFile = (notify) => {
//   notificationsStore.markAsRead(notify.history_uuid);
//   // dropdown.hide()
//   notificationsStore.downloadFile(notify.history_id);
// };

const markAllAsRead = async () => {
  allNotifications.value = allNotifications.value?.map((n) => {
    n.seen = true;
    return n;
  });
  await useAPI("/user/read-all-notifications", {
    method: "PUT",
  });
  // dropdown.hide()
};

const onOpenNotificationSlideover = () => {
  isNotificationsSlideoverOpen.value = true
  // markAllAsRead()
}

// watch isNotificationsSlideoverOpen for changes, if it's false, then mark all as read
watch(isNotificationsSlideoverOpen, (value) => {
  if (!value) {
    markAllAsRead()
  }
})
</script>
