<template>
    <div class="relative h-full">
        <a
            v-if="inputFile && !isUploading"
            @click="translateStore.clearInputFile()"
            class="absolute right-8 top-8 cursor-pointer inline-flex justify-center p-2 text-gray-500 rounded-full "
        >
            <svg
                class="w-6 h-6"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </a>
        <div v-if="showWarningDocument" class="px-4 py-4">
            <SignInSignUpRequire
                class="p-14"
                :title="$t('Want to create a speech from a document?')"
                :message="
                    $t('With the free version, you can create a speech from text input only. Sign up for free to create from documents.')
                "
                />
        </div>
        <div v-else class="flex items-center justify-center w-full p-6">
            <label
                id="dropzone-file-input"
                @drop.prevent="dropHandler"
                @dragover="dragOverHandler"
                for="dropzone-file"
                class="flex flex-col items-center rounded-lg justify-center w-full h-64 cursor-pointer border-2 border-dashed dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-950"
            >
                <div class="relative" v-show="inputFile">
                    <a
                        href="#"
                        class="relative flex md:min-w-[330px] flex-inline space-x-3 max-w-sm md:max-w-sm px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
                        :class="
                            isInvalidFile
                                ? 'border-2 border-yellow-500 dark:border-yellow-300'
                                : documentResult === 'ERROR'
                                ? 'border-2 border-red-500 dark:border-red-500'
                                : ''
                        "
                    >
                        <div
                            v-if="isInvalidFile"
                            class="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-yellow-500 dark:bg-yellow-300 border-2 border-white rounded-full -top-2 -right-2 dark:border-gray-900"
                        >
                            <svg
                                class="w-2.5 h-2.5"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 14 14"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                                />
                            </svg>
                        </div>
                        <div>
                            <svg
                                class="w-10 h-10 mb-3 text-gray-400"
                                aria-hidden="true"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </div>
                        <div class="truncate">
                            <p class="font-normal text-gray-700 dark:text-gray-400 truncate">
                                {{ inputFile?.name }}
                            </p>
                            <p class="text-sm font-thin text-gray-700 dark:text-gray-400">
                                {{ $fileSizeFormat(file_size || inputFile?.size) }}
                            </p>
                            <p v-if="isFileSizeExceed">
                                <small class="text-red-500 dark:text-red-400">
                                    {{ $t('File size exceeds the limit') }}
                                </small>
                            </p>
                        </div>

                    </a>

                    <div v-if="isInvalidFile" class="flex flex-col text-yellow-500 dark:text-yellow-300 mt-2">
                        <small>{{ $t('Invalid file format.') }}</small>
                        <small>{{ $t('Please convert to docx, pptx or xlsx before translating') }}</small>
                    </div>

                    <div
                        v-if="!uploadProgress && documentResult && documentResult === 'STARTED'"
                        class="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-green-500 border-2 border-white rounded-full -top-2 -right-2 dark:border-gray-900"
                    >
                        <svg
                            class="w-2.5 h-2.5"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 16 12"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M1 5.917 5.724 10.5 15 1.5"
                            />
                        </svg>
                    </div>
                    <div
                        v-if="uploadProgress"
                        class="absolute top-0 h-full bg-gray-500 dark:bg-gray-300 rounded-lg opacity-5 text-center"
                        :style="`width: ${uploadProgress}%`"
                    ></div>
                </div>
                <div
                    v-show="inputFile && documentResult !== 'STARTED' && !isFileProtected && !isTranslating"
                    class="flex items-center mt-2"
                >
                    <input
                        id="default-checkbox"
                        type="checkbox"
                        v-model="isFileHasPassword"
                        class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <label for="default-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{
                        $t('Is the file password protected?')
                    }}</label>
                </div>
                <div v-show="!inputFile" class="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg
                        aria-hidden="true"
                        class="w-10 h-10 mb-3 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        ></path>
                    </svg>
                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                        <span class="font-semibold">
                            {{ $t('Click to upload') }}
                        </span>
                        <br />
                        {{ $t('or drag and drop your file here') }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        <span v-for="fileType in supportFiles">
                            .{{ fileType }}
                            <span v-if="fileType !== supportFiles[supportFiles.length - 1]">, </span>
                        </span>
                    </p>
                    <p class="text-xs text-center text-gray-500 dark:text-gray-400 mt-1">
                        {{ $t("MAX. 500,000 rows of data") }} <br/>
                        {{ $t("And the maximum file size is 100MB.") }}
                    </p>
                </div>
                <div class="text-sm mt-2 text-gray-500 dark:text-gray-400">

                    <p
                        v-if="
                            documentResult !== 'STARTED' &&
                            (isFileProtected || (isFileHasPassword && inputFile && !isTranslating)) && 
                            ['pdf', 'docx'].includes(inputFileType as string)
                        "
                        class="text-red-500 mb-3"
                    >
                        <input
                            ref="filePasswordInput"
                            type="password"
                            id="password"
                            v-model="filePassword"
                            class="mt-2 md:min-w-[330px] bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            :placeholder="$t('Enter file password here')"
                            required
                        />
                        <div v-if="documentResult === 'ERROR'" class="mt-2">
                            {{ $t('Error') }}: {{ $t(translateError) }}
                        </div>
                    </p>
                    <p v-else-if="documentResult === 'ERROR'" class="text-red-500">
                        {{ $t('Error') }}: {{ $t(translateError) }}
                        <div v-if="translateError === 'NOT_VERIFY_ACCOUNT'" class="text-center mt-2">
                            <UButton
                            icon="i-ic-baseline-plus"
                            size="xs"
                            color="gray"
                            variant="solid"
                            :label="$t('Resend verify email')"
                            :trailing="false"
                            @click="authStore.resendVerifyEmail()"
                          />
                        </div>
                        <div v-if="['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(translateError)" class="text-center mt-2">
                            <UButton
                            icon="i-ic-baseline-plus"
                            size="xs"
                            color="gray"
                            variant="solid"
                            :label="$t('Buy more credits')"
                            :trailing="false"
                            @click="navigateTo('/profile/buy-credits')"
                          />
                        </div>
                    </p>
                    <p v-if="uploadProgress && uploadProgress < 100" class="font-medium animate-pulse">
                        {{ $t('Upload progress') }}: {{ uploadProgress }}%
                    </p>
                    <p
                        v-if="uploadProgress && uploadProgress === 100 && documentResult !== 'ERROR'"
                        class="font-medium animate-pulse"
                    >
                        {{ $t("File upload completed! Starting to create speech...") }}
                    </p>
                    <p v-if="!uploadProgress && documentResult && documentResult === 'STARTED'">
                        {{ $t("Started creating your speech...") }}
                    </p>
                </div>

                <!-- <div
                class="absolute bottom-8 right-8 text-right text-xs font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
            >
                <div v-if="inputFile && !isUploading"
                @click="translateStore.clearInputFile()">
                    <UButton :padded="false" color="red" variant="link" :label="$t('Clear document')" />
                </div>
            </div> -->
                <input
                    :value="fileInput"
                    id="dropzone-file"
                    type="file"
                    class="hidden"
                    @change="handleFileUpload"
                    :disabled="isUploading"
                    :accept="`application/pdf, .docx, .txt, .mobi, .epub, .pptx, .xlsx, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.openxmlformats-officedocument.presentationml.presentation, .html, .${supportFiles.join(', .')}`"
                />
            </label>
        </div>
        <div class="flex flex-col w-full px-6 text-sm">
            <div class="flex flex-row items-center gap-1 text-primary-500">
                <UIcon name="i-ri:information-2-fill" class="text-primary-500" />
                <div>
                    {{ $t("After the file is uploaded, we will start creating your speech. This may take a few minutes.") }}
                </div>
            </div>
            <ul class="list-disc pl-8">
                <li>
                    {{ $t("You will receive an email with a link to download the file") }}
                </li>
                <li>
                    {{ $t("Or you can download the file directly from the History") }}
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
const translateStore = useTranslateStore()
const { $fileSizeFormat } = useNuxtApp()
const {
    inputFile,
    isUploading,
    showCustomPrompt,
    inputFileType,
    translateOptions,
    uploadProgress,
    documentResult,
    isInvalidFile,
    translateError,
    isFileProtected,
    filePassword,
    isFileHasPassword,
    isTranslating,
    showWarningDocument,
    isFileSizeExceed,
    supportFiles,
    file_size
} = storeToRefs(translateStore)

const customPromptInput = ref(null)

const fileInput = ref(null)

const filePasswordInput = ref(null)

const handleFileUpload = (e: Event) => {
    const files = (e.target as HTMLInputElement).files
    if (files?.length > 0) {
        translateStore.clearInputFile()
        const file = files[0]
        translateStore.setInputFile(file)
        translateStore.setMode('document')
    }
}

const dragOverHandler = (e: Event) => {
    e.preventDefault()
}

const dropHandler = (e: Event) => {
    const files = e.dataTransfer.files
    if (files && files.length > 0) {
        const file = files[0]
        translateStore.clearInputFile()
        translateStore.setInputFile(file)
        translateStore.setMode('document')
    }
}

watch(showCustomPrompt, (value) => {
    if (value) {
        nextTick(() => {
            customPromptInput.value.focus()
        })
        translateOptions.value = {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
        }
    }
})

watch(isFileProtected, (value) => {
    if (value) {
        nextTick(() => {
            filePasswordInput.value.focus()
        })
    }
})
</script>
