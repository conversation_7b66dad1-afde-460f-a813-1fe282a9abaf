<template>
    <UButton
        block
        :ui="{
            rounded: 'rounded-md',
        }"
        size="xl"
        color="rose"
        icon="i-bi-stripe"
        :loading="loading['createOrderStripe']"
        @click="onPay"
        class="group"
    >
        {{ $t('Debit or Credit Card') }}
    </UButton>
</template>

<script setup lang="ts">
const { stripe, isLoading } = await useClientStripe()
const runtimeConfig = useRuntimeConfig()
const props = defineProps<{
    productId: string
    quantity: number
    specialBonusPercent: number
    totalPrice: number
    totalCredits: number
    specialBonusCredits: number
}>()

const paymentsStore = usePaymentsStore()
const { loading, isOpen } = storeToRefs(paymentsStore)
const openWarning = ref(false)

const onPay = async () => {
    const res: any = await paymentsStore.createOrderStripe(props.productId, props.quantity)
    if (res && res?.approval_url && res?.success) {
        // open new tab
        // window.open(res.approval_url, "_blank");

        // open new small window centered
        // const _window = window.open(res.approval_url, "Payment", "width=420,height=600,scrollbars=yes");
        // listen to close event
        popupCenter({ url: res.approval_url, title: 'Payment', w: 900, h: 900 })
        isOpen.value = false
    }
}

const popupCenter = ({ url, title, w, h }) => {
    // Fixes dual-screen position                             Most browsers      Firefox
    const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX
    const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY

    const width = window.innerWidth
        ? window.innerWidth
        : document.documentElement.clientWidth
        ? document.documentElement.clientWidth
        : screen.width
    const height = window.innerHeight
        ? window.innerHeight
        : document.documentElement.clientHeight
        ? document.documentElement.clientHeight
        : screen.height

    const systemZoom = width / window.screen.availWidth
    const left = (width - w) / 2 / systemZoom + dualScreenLeft
    const top = (height - h) / 2 / systemZoom + dualScreenTop
    const newWindow = window.open(
        url,
        title,
        `
      scrollbars=yes,
      width=${w / systemZoom}, 
      height=${h / systemZoom}, 
      top=${top}, 
      left=${left}
      `
    )

    if (window?.focus) newWindow?.focus()
    const interval = setInterval(() => {
        if (window?.closed) {
            const authStore = useAuthStore()
            clearInterval(interval)
            isOpen.value = false
            // sync token after 3 seconds
            setTimeout(() => {
                authStore.syncUserTokenInfo()
            }, 3000)
        }
    }, 500)
}
</script>
